
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/BtnController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0cfc6AzwvpKzLNWGyxSfqHD', 'BtnController');
// scripts/game/BtnController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var BtnController = /** @class */ (function (_super) {
    __extends(BtnController, _super);
    function BtnController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.settingBtn = null; // 设置按钮（固定不动）
        _this.settingPanel = null; // 设置面板容器
        _this.maskNode = null; // 遮罩节点，用于控制显示区域
        _this.gameplayBtn = null; // 玩法按钮
        _this.musicBtn = null; // 音乐开关按钮
        _this.soundBtn = null; // 音效开关按钮
        _this.infoDialogNode = null; // info_dialog节点引用
        // 音乐和音效开关状态
        _this.music = true;
        _this.sound = true;
        // 设置面板展开状态
        _this.isSettingExpanded = false;
        // 动画时长 - 可以调整这个值来控制动画速度
        _this.animationDuration = 0.5; // 增加到0.5秒，让动画更缓慢
        // 保存遮罩的原始高度
        _this.originalMaskHeight = 0;
        // 防止重复点击的标志
        _this.isAnimating = false;
        // 防止重复初始化音乐的标志
        _this.isMusicInitialized = false;
        // 全屏点击遮罩，用于点击屏幕任意位置关闭设置面板
        _this.clickMask = null;
        // 添加初始化标志，防止重复初始化
        _this.hasInitialized = false;
        return _this;
        // update (dt) {}
    }
    // LIFE-CYCLE CALLBACKS:
    BtnController.prototype.onLoad = function () {
        // 从本地存储获取音乐和音效开关状态
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
        // 初始化设置面板为隐藏状态
        this.initSettingPanel();
        // 初始化点击遮罩
        this.initClickMask();
    };
    BtnController.prototype.start = function () {
        var _this = this;
        // 预加载所有按钮资源，然后再设置按钮事件
        this.preloadButtonResources(function () {
            // 添加一个小延迟，确保所有资源都完全就绪
            _this.scheduleOnce(function () {
                // 设置主设置按钮点击事件 - 使用新的按压UI样式，播放音效
                _this.setupImageButton(_this.settingBtn, 'side_btn_menu_normal', 'side_btn_menu_pressed', function () {
                    _this.toggleSettingPanel();
                }, true); // 改为true，播放音效来确认点击是否真的被执行
                // 设置玩法按钮点击事件 - 打开info_dialog页面
                _this.setupImageButton(_this.gameplayBtn, 'side_btn_info_normal', 'side_btn_info_pressed', function () {
                    _this.openInfoDialog();
                });
                // 设置音乐按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupMusicButton();
                // 设置音效按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupSoundButton();
                // 初始化按钮状态 - 使用新的按钮UI样式
                _this.updateMusicButtonUI();
                _this.updateSoundButtonUI();
                // 立即检查按钮状态和确保按钮在最上层
                _this.checkButtonStates();
                _this.ensureButtonsOnTop();
            }, 0.2); // 等待200毫秒
        });
        // 只在首次加载时播放音乐，避免重置正在播放的音乐
        this.scheduleOnce(function () {
            _this.initializeMusicPlayback();
        }, 0.1);
    };
    // 确保所有按钮都在最上层
    BtnController.prototype.ensureButtonsOnTop = function () {
        var buttons = [this.settingBtn, this.gameplayBtn, this.musicBtn, this.soundBtn];
        buttons.forEach(function (btn, index) {
            if (btn) {
                // 设置合理的zIndex值，确保按钮在所有棋盘和UI元素之上
                btn.zIndex = cc.macro.MAX_ZINDEX - 10 + index;
            }
        });
        // 如果有设置面板，确保它的zIndex也足够高，但低于按钮
        if (this.settingPanel) {
            this.settingPanel.zIndex = cc.macro.MAX_ZINDEX - 20;
        }
        if (this.maskNode) {
            this.maskNode.zIndex = cc.macro.MAX_ZINDEX - 19;
        }
    };
    // 检查按钮状态的调试方法
    BtnController.prototype.checkButtonStates = function () {
        var buttons = [
            { name: "设置按钮", node: this.settingBtn },
            { name: "玩法按钮", node: this.gameplayBtn },
            { name: "音乐按钮", node: this.musicBtn },
            { name: "音效按钮", node: this.soundBtn }
        ];
        buttons.forEach(function (btn) {
            if (btn.node) {
            }
            else {
                console.error("BtnController: " + btn.name + "\u8282\u70B9\u672A\u8BBE\u7F6E");
            }
        });
    };
    /**
     * 设置图片按钮 - 支持按压状态切换
     */
    BtnController.prototype.setupImageButton = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        if (playSound === void 0) { playSound = true; }
        if (!node) {
            console.error("BtnController: setupImageButton - 节点为空", normalImg);
            return;
        }
        // 确保节点可以接收触摸事件
        node.active = true;
        // 将按钮移动到最上层，确保不被其他UI元素遮挡
        node.zIndex = cc.macro.MAX_ZINDEX - 5;
        // 检查是否有Button组件阻挡触摸事件
        var buttonComponent = node.getComponent(cc.Button);
        if (buttonComponent) {
            buttonComponent.enabled = false;
        }
        // 强制确保节点具有正确的触摸属性
        node._touchListener = null; // 清除可能存在的旧监听器
        // 设置初始状态为normal（使用预加载的资源，应该能立即设置成功）
        this.setButtonSprite(node, normalImg);
        // 延迟一帧注册触摸事件，确保图片设置完成
        this.scheduleOnce(function () {
            _this.registerButtonEvents(node, normalImg, pressedImg, clickCallback, playSound);
            // 再次确保按钮在最上层
            node.zIndex = cc.macro.MAX_ZINDEX - 5;
        }, 0.1);
    };
    // 注册按钮触摸事件
    BtnController.prototype.registerButtonEvents = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        // 清除之前的事件监听器，避免重复注册
        node.off(cc.Node.EventType.TOUCH_START);
        node.off(cc.Node.EventType.TOUCH_END);
        node.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加触摸事件
        node.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时切换到pressed状态
            _this.setButtonSprite(node, pressedImg);
        }, this);
        node.on(cc.Node.EventType.TOUCH_END, function () {
            // 抬起时切换回normal状态并执行点击回调
            _this.setButtonSprite(node, normalImg);
            // 根据参数决定是否播放按钮点击音效
            if (playSound) {
                _this.playButtonClickSound();
            }
            if (clickCallback) {
                clickCallback();
            }
        }, this);
        node.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时也要切换回normal状态
            _this.setButtonSprite(node, normalImg);
        }, this);
    };
    /**
     * 设置按钮精灵图片 - 优先使用预加载的缓存资源
     */
    BtnController.prototype.setButtonSprite = function (node, imageName) {
        if (!node || !imageName) {
            console.error("BtnController: setButtonSprite - 参数无效", { node: !!node, imageName: imageName });
            return;
        }
        // 使用Config中定义的按钮资源路径，不包含扩展名
        var imagePath = Config_1.Config.buttonRes + imageName;
        // 优先从缓存获取（预加载的资源应该已经在缓存中）
        var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
        if (cachedSpriteFrame) {
            // 图片已经在缓存中，直接使用
            var sprite = node.getComponent(cc.Sprite);
            if (sprite) {
                sprite.spriteFrame = cachedSpriteFrame;
                node.color = cc.Color.WHITE;
                node.opacity = 255;
                return; // 成功设置，直接返回
            }
            else {
                console.error("BtnController: 节点没有Sprite组件", node.name);
                return;
            }
        }
        // 如果缓存中没有，说明预加载可能失败了，进行备用加载
        console.warn("BtnController: 缓存中没有找到图片，进行备用加载", imagePath);
        cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
            if (!error && spriteFrame && node && node.isValid) {
                var sprite = node.getComponent(cc.Sprite);
                if (sprite) {
                    sprite.spriteFrame = spriteFrame;
                    node.color = cc.Color.WHITE;
                    node.opacity = 255;
                }
                else {
                    console.error("BtnController: 节点缺少Sprite组件", node.name);
                }
            }
            else {
                console.error("BtnController: 备用加载按钮图片失败", {
                    imagePath: imagePath,
                    nodeValid: node && node.isValid,
                    error: error ? error.message : "未知错误"
                });
            }
        });
    };
    /**
     * 清理按钮状态 - 移除可能导致高亮效果的设置
     */
    BtnController.prototype.cleanButtonState = function (node) {
        if (!node)
            return;
        // 重置节点颜色和透明度
        node.color = cc.Color.WHITE;
        node.opacity = 255;
    };
    /**
     * 设置音乐按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupMusicButton = function () {
        var _this = this;
        if (!this.musicBtn)
            return;
        // 更新按钮显示状态
        this.updateMusicButtonUI();
        // 添加触摸事件
        this.musicBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.music ? 'side_btn_music(on)_pressed' : 'side_btn_music(off)_pressed';
            _this.setButtonSprite(_this.musicBtn, currentImg);
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音乐状态 - 模仿SettingDialogController的实现
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
            _this.setButtonSprite(_this.musicBtn, newImgName);
            // 直接控制音乐播放，模仿SettingDialogController
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateMusicButtonUI();
        }, this);
    };
    /**
     * 设置音效按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupSoundButton = function () {
        var _this = this;
        if (!this.soundBtn)
            return;
        // 更新按钮显示状态
        this.updateSoundButtonUI();
        // 添加触摸事件
        this.soundBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.sound ? 'side_btn_sound(on)_pressed' : 'side_btn_sound(off)_pressed';
            _this.setButtonSprite(_this.soundBtn, currentImg);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音效状态
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
            _this.setButtonSprite(_this.soundBtn, newImgName);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateSoundButtonUI();
        }, this);
    };
    /**
     * 更新音乐按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateMusicButtonUI = function () {
        if (!this.musicBtn)
            return;
        var imgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
        this.setButtonSprite(this.musicBtn, imgName);
    };
    /**
     * 更新音效按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateSoundButtonUI = function () {
        if (!this.soundBtn)
            return;
        var imgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
        this.setButtonSprite(this.soundBtn, imgName);
    };
    /**
     * 播放按钮点击音效 - 模仿项目中的音效播放方式
     */
    BtnController.prototype.playButtonClickSound = function () {
        // 检查音效开关状态，只有在音效开启时才播放
        if (this.sound) {
            // 使用AudioManager的按键音效方法
            AudioManager_1.AudioManager.keyingToneAudio();
        }
    };
    /**
     * 初始化音乐播放 - 只在首次加载时播放，避免重置
     */
    BtnController.prototype.initializeMusicPlayback = function () {
        // 只在未初始化时播放音乐
        if (!this.isMusicInitialized) {
            this.isMusicInitialized = true;
            // 检查音乐开关状态，只有开启时才播放
            if (this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
        }
    };
    /**
     * 打开info_dialog页面 - 模仿项目中的实现方式
     */
    BtnController.prototype.openInfoDialog = function () {
        // 如果动画正在播放，禁止操作
        if (this.isAnimating) {
            return;
        }
        // 自动隐藏maskNode（收起设置面板）
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        // 模仿HallPageController中的实现方式
        if (this.infoDialogNode) {
            var infoDialogController = this.infoDialogNode.getComponent("InfoDialogController");
            if (infoDialogController) {
                // 调用show方法，传入空的回调函数
                infoDialogController.show(function () { });
            }
        }
    };
    /**
     * 预加载所有按钮资源 - 确保按钮图片在设置事件前就已加载完成
     */
    BtnController.prototype.preloadButtonResources = function (callback) {
        // 定义所有需要预加载的按钮资源
        var buttonImages = [
            'side_btn_menu_normal',
            'side_btn_menu_pressed',
            'side_btn_info_normal',
            'side_btn_info_pressed',
            'side_btn_music(on)_normal',
            'side_btn_music(on)_pressed',
            'side_btn_music(off)_normal',
            'side_btn_music(off)_pressed',
            'side_btn_sound(on)_normal',
            'side_btn_sound(on)_pressed',
            'side_btn_sound(off)_normal',
            'side_btn_sound(off)_pressed'
        ];
        var loadedCount = 0;
        var totalCount = buttonImages.length;
        // 如果没有需要加载的资源，直接回调
        if (totalCount === 0) {
            callback();
            return;
        }
        // 预加载每个资源
        buttonImages.forEach(function (imageName) {
            var imagePath = Config_1.Config.buttonRes + imageName;
            // 先检查是否已经在缓存中
            var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
            if (cachedSpriteFrame) {
                loadedCount++;
                if (loadedCount >= totalCount) {
                    callback();
                }
            }
            else {
                // 加载资源
                cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
                    if (!error && spriteFrame) {
                    }
                    else {
                        console.error("BtnController: \u6309\u94AE\u8D44\u6E90\u52A0\u8F7D\u5931\u8D25 [" + (loadedCount + 1) + "/" + totalCount + "]", imageName, error ? error.message : "未知错误");
                    }
                    loadedCount++;
                    if (loadedCount >= totalCount) {
                        callback();
                    }
                });
            }
        });
    };
    /**
     * 初始化设置面板状态
     */
    BtnController.prototype.initSettingPanel = function () {
        // 如果已经初始化过，跳过
        if (this.hasInitialized) {
            return;
        }
        if (this.settingPanel) {
            // 设置面板初始为隐藏状态
            this.settingPanel.active = false;
        }
        // 重要：设置mask节点的初始状态为隐藏（高度为0）
        if (this.maskNode) {
            this.maskNode.height = 0;
            this.maskNode.opacity = 255;
        }
        // 设置初始状态
        this.isSettingExpanded = false;
        this.isAnimating = false;
        this.hasInitialized = true;
        // 确保点击遮罩在初始化时是禁用的
        this.disableClickMask();
    };
    /**
     * 切换设置面板的展开/收起状态
     */
    BtnController.prototype.toggleSettingPanel = function () {
        // 防止动画进行中的重复点击
        if (this.isAnimating) {
            return; // 添加return语句，防止继续执行
        }
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        else {
            this.showSettingPanel();
        }
    };
    /**
     * 展开设置面板 - mask的size.y从0缓慢增加到275
     */
    BtnController.prototype.showSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            console.error("BtnController: showSettingPanel - maskNode为空");
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = true;
        // 显示settingPanel
        if (this.settingPanel) {
            this.settingPanel.active = true;
        }
        // 设置mask初始状态：高度为0，透明度为正常
        this.maskNode.height = 0;
        this.maskNode.opacity = 255;
        // 确保按钮始终在最上层
        this.ensureButtonsOnTop();
        // 执行展开动画 - mask高度从0到275
        cc.tween(this.maskNode)
            .to(this.animationDuration, {
            height: 275
        }, {
            easing: 'quartOut'
        })
            .call(function () {
            // 动画完成，解除动画锁定
            _this.isAnimating = false;
            // 启用点击遮罩，允许点击屏幕任意位置关闭面板
            _this.enableClickMask();
        })
            .start();
    };
    /**
     * 收起设置面板 - mask的size.y从275缓慢减少到0
     */
    BtnController.prototype.hideSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = false;
        // 禁用点击遮罩
        this.disableClickMask();
        // 执行收起动画 - mask高度从275缓慢减少到0，同时添加渐隐效果
        cc.tween(this.maskNode)
            .to(this.animationDuration * 0.7, {
            height: 0
        }, {
            easing: 'quartOut'
        })
            .to(this.animationDuration * 0.3, {
            opacity: 0
        }, {
            easing: 'quartIn' // 最后阶段快速渐隐
        })
            .call(function () {
            // 动画完成后隐藏面板，解除动画锁定，恢复透明度
            if (_this.settingPanel) {
                _this.settingPanel.active = false;
            }
            _this.maskNode.opacity = 255; // 恢复透明度，为下次展开做准备
            _this.isAnimating = false;
            // 确保按钮始终在最上层
            _this.ensureButtonsOnTop();
        })
            .start();
    };
    /**
     * 初始化点击遮罩
     */
    BtnController.prototype.initClickMask = function () {
        // 创建一个全屏的透明遮罩节点
        this.clickMask = new cc.Node("ClickMask");
        // 添加Widget组件，让遮罩铺满整个屏幕
        var widget = this.clickMask.addComponent(cc.Widget);
        widget.isAlignTop = true;
        widget.isAlignBottom = true;
        widget.isAlignLeft = true;
        widget.isAlignRight = true;
        widget.top = 0;
        widget.bottom = 0;
        widget.left = 0;
        widget.right = 0;
        // 添加Button组件，确保能接收点击事件
        var button = this.clickMask.addComponent(cc.Button);
        button.transition = cc.Button.Transition.NONE; // 不要视觉过渡效果
        // 添加点击事件监听
        button.node.on('click', this.onClickMaskClick, this);
        // 添加到Canvas，但设置为不可见
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.clickMask);
            // 设置为较低层级，但要高于棋盘等游戏元素
            this.clickMask.zIndex = 100;
        }
        // 默认禁用
        this.clickMask.active = false;
    };
    /**
     * 启用点击遮罩
     */
    BtnController.prototype.enableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = true;
        }
    };
    /**
     * 禁用点击遮罩
     */
    BtnController.prototype.disableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = false;
        }
    };
    /**
     * 点击遮罩的处理函数
     */
    BtnController.prototype.onClickMaskClick = function () {
        // 检查面板是否展开且不在动画中
        if (this.isSettingExpanded && !this.isAnimating) {
            // 关闭设置面板，相当于再次点击设置按钮
            this.hideSettingPanel();
        }
    };
    BtnController.prototype.onDestroy = function () {
        // 清理点击遮罩
        if (this.clickMask && this.clickMask.isValid) {
            this.clickMask.off('click', this.onClickMaskClick, this);
            this.clickMask.removeFromParent();
            this.clickMask = null;
        }
    };
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingPanel", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "maskNode", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "gameplayBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "infoDialogNode", void 0);
    BtnController = __decorate([
        ccclass
    ], BtnController);
    return BtnController;
}(cc.Component));
exports.default = BtnController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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