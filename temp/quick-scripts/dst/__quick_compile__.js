
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/bean/GameBean":4,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/resources/i18n/zh_HK":7,"./assets/meshTools/Singleton":8,"./assets/scripts/common/GameData":9,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/net/HttpManager":11,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/TipsDialogController":15,"./assets/scripts/GlobalManagerController":16,"./assets/scripts/common/GameMgr":17,"./assets/scripts/bean/EnumBean":18,"./assets/scripts/common/GameTools":19,"./assets/scripts/bean/LanguageType":20,"./assets/scripts/common/EventCenter":21,"./assets/scripts/common/MineConsole":22,"./assets/scripts/game/BtnController":23,"./assets/scripts/game/CongratsDialogController":24,"./assets/scripts/bean/GlobalBean":25,"./assets/scripts/game/AIManagedDialogController":26,"./assets/scripts/game/GamePageController":27,"./assets/meshTools/BaseSDK":28,"./assets/scripts/hall/HallCenterLayController":29,"./assets/scripts/game/Chess/HexChessBoardController":30,"./assets/scripts/game/Chess/ChessBoardController":31,"./assets/scripts/game/Chess/HexSingleChessBoardController":32,"./assets/scripts/ToastController":33,"./assets/scripts/hall/HallJoinRoomController":34,"./assets/scripts/hall/HallPageController":35,"./assets/scripts/hall/HallParentController":36,"./assets/scripts/hall/InfoDialogController":37,"./assets/scripts/hall/LevelSelectDemo":38,"./assets/scripts/hall/MatchParentController":39,"./assets/scripts/hall/LeaveDialogController":40,"./assets/scripts/hall/KickOutDialogController":41,"./assets/scripts/hall/HallCreateRoomController":42,"./assets/scripts/hall/SettingDialogController":43,"./assets/scripts/hall/TopUpDialogController":44,"./assets/scripts/hall/Level/LevelSelectPageController":45,"./assets/scripts/hall/Level/ScrollViewHelper":46,"./assets/scripts/hall/Level/LevelSelectExample":47,"./assets/scripts/hall/HallAutoController":48,"./assets/scripts/game/GameScoreController":49,"./assets/scripts/net/GameServerUrl":50,"./assets/scripts/net/HttpUtils":51,"./assets/scripts/net/IHttpMsgBody":52,"./assets/scripts/net/MessageBaseBean":53,"./assets/scripts/net/ErrorCode":54,"./assets/scripts/hall/PlayerLayoutController":55,"./assets/scripts/net/WebSocketTool":56,"./assets/scripts/pfb/PlayerGameController ":57,"./assets/scripts/pfb/PlayerScoreController":58,"./assets/scripts/pfb/MatchItemController":59,"./assets/scripts/pfb/SeatItemController":60,"./assets/scripts/net/MessageId":61,"./assets/scripts/hall/Level/LevelItemController":62,"./assets/scripts/net/WebSocketManager":63,"./assets/scripts/pfb/CongratsItemController":64,"./assets/scripts/util/Config":65,"./assets/scripts/util/LocalStorageManager":66,"./assets/scripts/util/NickNameLabel":67,"./assets/scripts/util/Dictionary":68,"./assets/scripts/util/AudioManager":69,"./assets/meshTools/MeshTools":70,"./assets/scripts/pfb/InfoItemOneController":71,"./assets/scripts/util/Tools":72,"./assets/scripts/game/Chess/SingleChessBoardController":73,"./assets/scripts/util/BlockingQueue":74,"./assets/scripts/start_up/StartUpCenterController":75,"./assets/resources/i18n/en":76,"./assets/resources/i18n/zh_CN":77,"./assets/meshTools/tools/Publish":78,"./assets/meshTools/tools/MeshSdkApi":79},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":46},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":70,"../../meshTools/Singleton":8,"../net/GameServerUrl":50},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../hall/LeaveDialogController":40,"../util/Tools":72,"../util/Config":65,"../GlobalManagerController":16,"../game/Chess/SingleChessBoardController":73,"../game/Chess/HexSingleChessBoardController":32,"../net/WebSocketManager":63,"../net/MessageId":61,"../common/GameMgr":17,"../common/EventCenter":21},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{"./HttpUtils":51,"./MessageBaseBean":53,"./GameServerUrl":50,"../../meshTools/MeshTools":70,"../common/GameMgr":17,"../common/EventCenter":21},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":17,"./StartUpCenterController":75},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./Config":65,"./Dictionary":68},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"./util/Config":65,"./util/Tools":72},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../meshTools/MeshTools":70,"../meshTools/tools/Publish":78,"./bean/EnumBean":18,"./bean/GlobalBean":25,"./bean/LanguageType":20,"./common/EventCenter":21,"./common/GameMgr":17,"./game/GamePageController":27,"./hall/HallPageController":35,"./level/LevelPageController":10,"./hall/TopUpDialogController":44,"./net/ErrorCode":54,"./net/GameServerUrl":50,"./net/MessageBaseBean":53,"./net/MessageId":61,"./net/WebSocketManager":63,"./net/WebSocketTool":56,"./start_up/StartUpPageController":13,"./TipsDialogController":15,"./ToastController":33,"./util/AudioMgr":14,"./util/Config":65},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":79,"./EventCenter":21,"./GameData":9,"./GameTools":19,"./MineConsole":22},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":17},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../util/AudioManager":69,"../util/Config":65,"../util/LocalStorageManager":66},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../bean/GlobalBean":25,"../common/EventCenter":21,"../common/GameMgr":17,"../net/MessageBaseBean":53,"../pfb/CongratsItemController":64,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":48},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../net/WebSocketManager":63,"../net/MessageId":61,"../util/Config":65},"path":"preview-scripts/assets/scripts/game/AIManagedDialogController.js"},{"deps":{"../bean/GlobalBean":25,"../hall/LeaveDialogController":40,"../util/AudioManager":69,"../util/Config":65,"../util/Tools":72,"./CongratsDialogController":24,"./GameScoreController":49,"./Chess/ChessBoardController":31,"./Chess/HexChessBoardController":30,"../pfb/PlayerGameController ":57,"../net/WebSocketManager":63,"../net/MessageId":61,"./AIManagedDialogController":26},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../bean/GlobalBean":25,"../net/MessageId":61,"../net/WebSocketManager":63,"../ToastController":33,"./HallAutoController":48,"./HallCreateRoomController":42,"./HallJoinRoomController":34},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../../bean/GlobalBean":25,"../../pfb/PlayerGameController ":57},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../../bean/GlobalBean":25,"../../pfb/PlayerGameController ":57},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../../net/WebSocketManager":63,"../../net/MessageId":61},"path":"preview-scripts/assets/scripts/game/Chess/HexSingleChessBoardController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../bean/GlobalBean":25,"../common/GameMgr":17,"../net/MessageId":61,"../net/WebSocketManager":63,"../net/WebSocketTool":56,"../ToastController":33,"../util/AudioManager":69,"./HallParentController":36,"./InfoDialogController":37,"./KickOutDialogController":41,"./LeaveDialogController":40,"./Level/LevelSelectPageController":45,"./MatchParentController":39,"./SettingDialogController":43},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":25,"../common/GameMgr":17,"../net/MessageId":61,"../net/WebSocketManager":63,"../ToastController":33,"../util/Config":65,"../util/Tools":72,"./HallCenterLayController":29},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":25,"../common/EventCenter":21,"../common/GameMgr":17,"../net/MessageBaseBean":53,"../pfb/MatchItemController":59,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../common/GameMgr":17,"../net/MessageId":61,"../net/WebSocketManager":63,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../net/MessageId":61,"../net/WebSocketManager":63,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../bean/GlobalBean":25,"../pfb/SeatItemController":60,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/AudioManager":69,"../util/Config":65,"../util/LocalStorageManager":66,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../common/GameMgr":17,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../../GlobalManagerController":16,"./LevelSelectController":6,"../../net/MessageId":61,"../../net/WebSocketManager":63},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../bean/GlobalBean":25,"../util/Config":65,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../bean/GlobalBean":25,"../pfb/PlayerScoreController":58},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../bean/GlobalBean":25,"../util/Tools":72},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"./MessageBaseBean":53,"./MessageId":61,"../util/Tools":72,"../../meshTools/Singleton":8,"../common/EventCenter":21,"../common/GameMgr":17},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../util/Tools":72},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../bean/GlobalBean":25,"../util/NickNameLabel":67,"../util/Tools":72},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../util/NickNameLabel":67,"../util/Tools":72},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../util/NickNameLabel":67,"../util/Tools":72},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":21,"../common/GameMgr":17,"./WebSocketTool":56},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/Config":65,"../util/NickNameLabel":67,"../util/Tools":72},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":66},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./tools/Publish":78},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"./AudioManager":69,"./Config":65},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"../../net/WebSocketManager":63,"../../net/MessageId":61},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../common/EventCenter":21,"../common/GameMgr":17,"../net/MessageBaseBean":53,"../util/Config":65},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../MeshTools":70,"../BaseSDK":28,"../../scripts/net/MessageBaseBean":53,"../../scripts/common/GameMgr":17,"../../scripts/common/EventCenter":21,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    