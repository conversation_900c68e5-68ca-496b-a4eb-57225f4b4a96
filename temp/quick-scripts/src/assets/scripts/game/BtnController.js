"use strict";
cc._RF.push(module, '0cfc6AzwvpKzLNWGyxSfqHD', 'BtnController');
// scripts/game/BtnController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var BtnController = /** @class */ (function (_super) {
    __extends(BtnController, _super);
    function BtnController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.settingBtn = null; // 设置按钮（固定不动）
        _this.settingPanel = null; // 设置面板容器
        _this.maskNode = null; // 遮罩节点，用于控制显示区域
        _this.gameplayBtn = null; // 玩法按钮
        _this.musicBtn = null; // 音乐开关按钮
        _this.soundBtn = null; // 音效开关按钮
        _this.infoDialogNode = null; // info_dialog节点引用
        // 音乐和音效开关状态
        _this.music = true;
        _this.sound = true;
        // 设置面板展开状态
        _this.isSettingExpanded = false;
        // 动画时长 - 可以调整这个值来控制动画速度
        _this.animationDuration = 0.5; // 增加到0.5秒，让动画更缓慢
        // 保存遮罩的原始高度
        _this.originalMaskHeight = 0;
        // 防止重复点击的标志
        _this.isAnimating = false;
        // 防止重复初始化音乐的标志
        _this.isMusicInitialized = false;
        // 全屏点击遮罩，用于点击屏幕任意位置关闭设置面板
        _this.clickMask = null;
        // 添加初始化标志，防止重复初始化
        _this.hasInitialized = false;
        return _this;
        // update (dt) {}
    }
    // LIFE-CYCLE CALLBACKS:
    BtnController.prototype.onLoad = function () {
        // 从本地存储获取音乐和音效开关状态
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
        // 初始化设置面板为隐藏状态
        this.initSettingPanel();
        // 初始化点击遮罩
        this.initClickMask();
    };
    BtnController.prototype.start = function () {
        var _this = this;
        // 预加载所有按钮资源，然后再设置按钮事件
        this.preloadButtonResources(function () {
            // 添加一个小延迟，确保所有资源都完全就绪
            _this.scheduleOnce(function () {
                // 设置主设置按钮点击事件 - 使用新的按压UI样式，播放音效
                _this.setupImageButton(_this.settingBtn, 'side_btn_menu_normal', 'side_btn_menu_pressed', function () {
                    _this.toggleSettingPanel();
                }, true); // 改为true，播放音效来确认点击是否真的被执行
                // 设置玩法按钮点击事件 - 打开info_dialog页面
                _this.setupImageButton(_this.gameplayBtn, 'side_btn_info_normal', 'side_btn_info_pressed', function () {
                    _this.openInfoDialog();
                });
                // 设置音乐按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupMusicButton();
                // 设置音效按钮点击事件 - 使用新的UI样式和开关功能
                _this.setupSoundButton();
                // 初始化按钮状态 - 使用新的按钮UI样式
                _this.updateMusicButtonUI();
                _this.updateSoundButtonUI();
                // 立即检查按钮状态和确保按钮在最上层
                _this.checkButtonStates();
                _this.ensureButtonsOnTop();
            }, 0.2); // 等待200毫秒
        });
        // 只在首次加载时播放音乐，避免重置正在播放的音乐
        this.scheduleOnce(function () {
            _this.initializeMusicPlayback();
        }, 0.1);
    };
    // 确保所有按钮都在最上层
    BtnController.prototype.ensureButtonsOnTop = function () {
        var buttons = [this.settingBtn, this.gameplayBtn, this.musicBtn, this.soundBtn];
        buttons.forEach(function (btn, index) {
            if (btn) {
                // 设置合理的zIndex值，确保按钮在所有棋盘和UI元素之上
                btn.zIndex = cc.macro.MAX_ZINDEX - 10 + index;
            }
        });
        // 如果有设置面板，确保它的zIndex也足够高，但低于按钮
        if (this.settingPanel) {
            this.settingPanel.zIndex = cc.macro.MAX_ZINDEX - 20;
        }
        if (this.maskNode) {
            this.maskNode.zIndex = cc.macro.MAX_ZINDEX - 19;
        }
    };
    // 检查按钮状态的调试方法
    BtnController.prototype.checkButtonStates = function () {
        var buttons = [
            { name: "设置按钮", node: this.settingBtn },
            { name: "玩法按钮", node: this.gameplayBtn },
            { name: "音乐按钮", node: this.musicBtn },
            { name: "音效按钮", node: this.soundBtn }
        ];
        buttons.forEach(function (btn) {
            if (btn.node) {
            }
            else {
                console.error("BtnController: " + btn.name + "\u8282\u70B9\u672A\u8BBE\u7F6E");
            }
        });
    };
    /**
     * 设置图片按钮 - 支持按压状态切换
     */
    BtnController.prototype.setupImageButton = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        if (playSound === void 0) { playSound = true; }
        if (!node) {
            console.error("BtnController: setupImageButton - 节点为空", normalImg);
            return;
        }
        // 确保节点可以接收触摸事件
        node.active = true;
        // 将按钮移动到最上层，确保不被其他UI元素遮挡
        node.zIndex = cc.macro.MAX_ZINDEX - 5;
        // 检查是否有Button组件阻挡触摸事件
        var buttonComponent = node.getComponent(cc.Button);
        if (buttonComponent) {
            buttonComponent.enabled = false;
        }
        // 强制确保节点具有正确的触摸属性
        node._touchListener = null; // 清除可能存在的旧监听器
        // 设置初始状态为normal（使用预加载的资源，应该能立即设置成功）
        this.setButtonSprite(node, normalImg);
        // 延迟一帧注册触摸事件，确保图片设置完成
        this.scheduleOnce(function () {
            _this.registerButtonEvents(node, normalImg, pressedImg, clickCallback, playSound);
            // 再次确保按钮在最上层
            node.zIndex = cc.macro.MAX_ZINDEX - 5;
        }, 0.1);
    };
    // 注册按钮触摸事件
    BtnController.prototype.registerButtonEvents = function (node, normalImg, pressedImg, clickCallback, playSound) {
        var _this = this;
        // 清除之前的事件监听器，避免重复注册
        node.off(cc.Node.EventType.TOUCH_START);
        node.off(cc.Node.EventType.TOUCH_END);
        node.off(cc.Node.EventType.TOUCH_CANCEL);
        // 添加触摸事件
        node.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时切换到pressed状态
            _this.setButtonSprite(node, pressedImg);
        }, this);
        node.on(cc.Node.EventType.TOUCH_END, function () {
            // 抬起时切换回normal状态并执行点击回调
            _this.setButtonSprite(node, normalImg);
            // 根据参数决定是否播放按钮点击音效
            if (playSound) {
                _this.playButtonClickSound();
            }
            if (clickCallback) {
                clickCallback();
            }
        }, this);
        node.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时也要切换回normal状态
            _this.setButtonSprite(node, normalImg);
        }, this);
    };
    /**
     * 设置按钮精灵图片 - 优先使用预加载的缓存资源
     */
    BtnController.prototype.setButtonSprite = function (node, imageName) {
        if (!node || !imageName) {
            console.error("BtnController: setButtonSprite - 参数无效", { node: !!node, imageName: imageName });
            return;
        }
        // 使用Config中定义的按钮资源路径，不包含扩展名
        var imagePath = Config_1.Config.buttonRes + imageName;
        // 优先从缓存获取（预加载的资源应该已经在缓存中）
        var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
        if (cachedSpriteFrame) {
            // 图片已经在缓存中，直接使用
            var sprite = node.getComponent(cc.Sprite);
            if (sprite) {
                sprite.spriteFrame = cachedSpriteFrame;
                node.color = cc.Color.WHITE;
                node.opacity = 255;
                return; // 成功设置，直接返回
            }
            else {
                console.error("BtnController: 节点没有Sprite组件", node.name);
                return;
            }
        }
        // 如果缓存中没有，说明预加载可能失败了，进行备用加载
        console.warn("BtnController: 缓存中没有找到图片，进行备用加载", imagePath);
        cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
            if (!error && spriteFrame && node && node.isValid) {
                var sprite = node.getComponent(cc.Sprite);
                if (sprite) {
                    sprite.spriteFrame = spriteFrame;
                    node.color = cc.Color.WHITE;
                    node.opacity = 255;
                }
                else {
                    console.error("BtnController: 节点缺少Sprite组件", node.name);
                }
            }
            else {
                console.error("BtnController: 备用加载按钮图片失败", {
                    imagePath: imagePath,
                    nodeValid: node && node.isValid,
                    error: error ? error.message : "未知错误"
                });
            }
        });
    };
    /**
     * 清理按钮状态 - 移除可能导致高亮效果的设置
     */
    BtnController.prototype.cleanButtonState = function (node) {
        if (!node)
            return;
        // 重置节点颜色和透明度
        node.color = cc.Color.WHITE;
        node.opacity = 255;
    };
    /**
     * 设置音乐按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupMusicButton = function () {
        var _this = this;
        if (!this.musicBtn)
            return;
        // 更新按钮显示状态
        this.updateMusicButtonUI();
        // 添加触摸事件
        this.musicBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.music ? 'side_btn_music(on)_pressed' : 'side_btn_music(off)_pressed';
            _this.setButtonSprite(_this.musicBtn, currentImg);
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音乐状态 - 模仿SettingDialogController的实现
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
            _this.setButtonSprite(_this.musicBtn, newImgName);
            // 直接控制音乐播放，模仿SettingDialogController
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        }, this);
        this.musicBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateMusicButtonUI();
        }, this);
    };
    /**
     * 设置音效按钮 - 支持开关状态和按压效果
     */
    BtnController.prototype.setupSoundButton = function () {
        var _this = this;
        if (!this.soundBtn)
            return;
        // 更新按钮显示状态
        this.updateSoundButtonUI();
        // 添加触摸事件
        this.soundBtn.on(cc.Node.EventType.TOUCH_START, function () {
            // 按下时显示pressed状态
            var currentImg = _this.sound ? 'side_btn_sound(on)_pressed' : 'side_btn_sound(off)_pressed';
            _this.setButtonSprite(_this.soundBtn, currentImg);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_END, function () {
            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）
            _this.playButtonClickSound();
            // 切换音效状态
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            // 立即更新按钮UI到新状态的normal图片
            var newImgName = _this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
            _this.setButtonSprite(_this.soundBtn, newImgName);
        }, this);
        this.soundBtn.on(cc.Node.EventType.TOUCH_CANCEL, function () {
            // 取消时恢复normal状态
            _this.updateSoundButtonUI();
        }, this);
    };
    /**
     * 更新音乐按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateMusicButtonUI = function () {
        if (!this.musicBtn)
            return;
        var imgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';
        this.setButtonSprite(this.musicBtn, imgName);
    };
    /**
     * 更新音效按钮UI - 根据开关状态显示对应图片
     */
    BtnController.prototype.updateSoundButtonUI = function () {
        if (!this.soundBtn)
            return;
        var imgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';
        this.setButtonSprite(this.soundBtn, imgName);
    };
    /**
     * 播放按钮点击音效 - 模仿项目中的音效播放方式
     */
    BtnController.prototype.playButtonClickSound = function () {
        // 检查音效开关状态，只有在音效开启时才播放
        if (this.sound) {
            // 使用AudioManager的按键音效方法
            AudioManager_1.AudioManager.keyingToneAudio();
        }
    };
    /**
     * 初始化音乐播放 - 只在首次加载时播放，避免重置
     */
    BtnController.prototype.initializeMusicPlayback = function () {
        // 只在未初始化时播放音乐
        if (!this.isMusicInitialized) {
            this.isMusicInitialized = true;
            // 检查音乐开关状态，只有开启时才播放
            if (this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
        }
    };
    /**
     * 打开info_dialog页面 - 模仿项目中的实现方式
     */
    BtnController.prototype.openInfoDialog = function () {
        // 如果动画正在播放，禁止操作
        if (this.isAnimating) {
            return;
        }
        // 自动隐藏maskNode（收起设置面板）
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        // 模仿HallPageController中的实现方式
        if (this.infoDialogNode) {
            var infoDialogController = this.infoDialogNode.getComponent("InfoDialogController");
            if (infoDialogController) {
                // 调用show方法，传入空的回调函数
                infoDialogController.show(function () { });
            }
        }
    };
    /**
     * 预加载所有按钮资源 - 确保按钮图片在设置事件前就已加载完成
     */
    BtnController.prototype.preloadButtonResources = function (callback) {
        // 定义所有需要预加载的按钮资源
        var buttonImages = [
            'side_btn_menu_normal',
            'side_btn_menu_pressed',
            'side_btn_info_normal',
            'side_btn_info_pressed',
            'side_btn_music(on)_normal',
            'side_btn_music(on)_pressed',
            'side_btn_music(off)_normal',
            'side_btn_music(off)_pressed',
            'side_btn_sound(on)_normal',
            'side_btn_sound(on)_pressed',
            'side_btn_sound(off)_normal',
            'side_btn_sound(off)_pressed'
        ];
        var loadedCount = 0;
        var totalCount = buttonImages.length;
        // 如果没有需要加载的资源，直接回调
        if (totalCount === 0) {
            callback();
            return;
        }
        // 预加载每个资源
        buttonImages.forEach(function (imageName) {
            var imagePath = Config_1.Config.buttonRes + imageName;
            // 先检查是否已经在缓存中
            var cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame);
            if (cachedSpriteFrame) {
                loadedCount++;
                if (loadedCount >= totalCount) {
                    callback();
                }
            }
            else {
                // 加载资源
                cc.resources.load(imagePath, cc.SpriteFrame, function (error, spriteFrame) {
                    if (!error && spriteFrame) {
                    }
                    else {
                        console.error("BtnController: \u6309\u94AE\u8D44\u6E90\u52A0\u8F7D\u5931\u8D25 [" + (loadedCount + 1) + "/" + totalCount + "]", imageName, error ? error.message : "未知错误");
                    }
                    loadedCount++;
                    if (loadedCount >= totalCount) {
                        callback();
                    }
                });
            }
        });
    };
    /**
     * 初始化设置面板状态
     */
    BtnController.prototype.initSettingPanel = function () {
        // 如果已经初始化过，跳过
        if (this.hasInitialized) {
            return;
        }
        if (this.settingPanel) {
            // 设置面板初始为隐藏状态
            this.settingPanel.active = false;
        }
        // 重要：设置mask节点的初始状态为隐藏（高度为0）
        if (this.maskNode) {
            this.maskNode.height = 0;
            this.maskNode.opacity = 255;
        }
        // 设置初始状态
        this.isSettingExpanded = false;
        this.isAnimating = false;
        this.hasInitialized = true;
        // 确保点击遮罩在初始化时是禁用的
        this.disableClickMask();
    };
    /**
     * 切换设置面板的展开/收起状态
     */
    BtnController.prototype.toggleSettingPanel = function () {
        // 防止动画进行中的重复点击
        if (this.isAnimating) {
            return; // 添加return语句，防止继续执行
        }
        if (this.isSettingExpanded) {
            this.hideSettingPanel();
        }
        else {
            this.showSettingPanel();
        }
    };
    /**
     * 展开设置面板 - mask的size.y从0缓慢增加到275
     */
    BtnController.prototype.showSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            console.error("BtnController: showSettingPanel - maskNode为空");
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = true;
        // 显示settingPanel
        if (this.settingPanel) {
            this.settingPanel.active = true;
        }
        // 设置mask初始状态：高度为0，透明度为正常
        this.maskNode.height = 0;
        this.maskNode.opacity = 255;
        // 确保按钮始终在最上层
        this.ensureButtonsOnTop();
        // 执行展开动画 - mask高度从0到275
        cc.tween(this.maskNode)
            .to(this.animationDuration, {
            height: 275
        }, {
            easing: 'quartOut'
        })
            .call(function () {
            // 动画完成，解除动画锁定
            _this.isAnimating = false;
            // 启用点击遮罩，允许点击屏幕任意位置关闭面板
            _this.enableClickMask();
        })
            .start();
    };
    /**
     * 收起设置面板 - mask的size.y从275缓慢减少到0
     */
    BtnController.prototype.hideSettingPanel = function () {
        var _this = this;
        if (!this.maskNode) {
            return;
        }
        // 立即更新状态，防止重复点击
        this.isAnimating = true;
        this.isSettingExpanded = false;
        // 禁用点击遮罩
        this.disableClickMask();
        // 执行收起动画 - mask高度从275缓慢减少到0，同时添加渐隐效果
        cc.tween(this.maskNode)
            .to(this.animationDuration * 0.7, {
            height: 0
        }, {
            easing: 'quartOut'
        })
            .to(this.animationDuration * 0.3, {
            opacity: 0
        }, {
            easing: 'quartIn' // 最后阶段快速渐隐
        })
            .call(function () {
            // 动画完成后隐藏面板，解除动画锁定，恢复透明度
            if (_this.settingPanel) {
                _this.settingPanel.active = false;
            }
            _this.maskNode.opacity = 255; // 恢复透明度，为下次展开做准备
            _this.isAnimating = false;
            // 确保按钮始终在最上层
            _this.ensureButtonsOnTop();
        })
            .start();
    };
    /**
     * 初始化点击遮罩
     */
    BtnController.prototype.initClickMask = function () {
        // 创建一个全屏的透明遮罩节点
        this.clickMask = new cc.Node("ClickMask");
        // 添加Widget组件，让遮罩铺满整个屏幕
        var widget = this.clickMask.addComponent(cc.Widget);
        widget.isAlignTop = true;
        widget.isAlignBottom = true;
        widget.isAlignLeft = true;
        widget.isAlignRight = true;
        widget.top = 0;
        widget.bottom = 0;
        widget.left = 0;
        widget.right = 0;
        // 添加Button组件，确保能接收点击事件
        var button = this.clickMask.addComponent(cc.Button);
        button.transition = cc.Button.Transition.NONE; // 不要视觉过渡效果
        // 添加点击事件监听
        button.node.on('click', this.onClickMaskClick, this);
        // 添加到Canvas，但设置为不可见
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.clickMask);
            // 设置为较低层级，但要高于棋盘等游戏元素
            this.clickMask.zIndex = 100;
        }
        // 默认禁用
        this.clickMask.active = false;
    };
    /**
     * 启用点击遮罩
     */
    BtnController.prototype.enableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = true;
        }
    };
    /**
     * 禁用点击遮罩
     */
    BtnController.prototype.disableClickMask = function () {
        if (this.clickMask) {
            this.clickMask.active = false;
        }
    };
    /**
     * 点击遮罩的处理函数
     */
    BtnController.prototype.onClickMaskClick = function () {
        // 检查面板是否展开且不在动画中
        if (this.isSettingExpanded && !this.isAnimating) {
            // 关闭设置面板，相当于再次点击设置按钮
            this.hideSettingPanel();
        }
    };
    BtnController.prototype.onDestroy = function () {
        // 清理点击遮罩
        if (this.clickMask && this.clickMask.isValid) {
            this.clickMask.off('click', this.onClickMaskClick, this);
            this.clickMask.removeFromParent();
            this.clickMask = null;
        }
    };
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "settingPanel", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "maskNode", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "gameplayBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Node)
    ], BtnController.prototype, "infoDialogNode", void 0);
    BtnController = __decorate([
        ccclass
    ], BtnController);
    return BtnController;
}(cc.Component));
exports.default = BtnController;

cc._RF.pop();